// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dynamic_notification_processor.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dynamicNotificationProcessorHash() =>
    r'd66c3d5007a143aa29f43d5528f7ef33828dbc7c';

/// See also [dynamicNotificationProcessor].
@ProviderFor(dynamicNotificationProcessor)
final dynamicNotificationProcessorProvider =
    AutoDisposeProvider<DynamicNotificationProcessor>.internal(
  dynamicNotificationProcessor,
  name: r'dynamicNotificationProcessorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dynamicNotificationProcessorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DynamicNotificationProcessorRef
    = AutoDisposeProviderRef<DynamicNotificationProcessor>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
