import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:vocadex/src/features/notifications/models/notification_model.dart';
import 'package:vocadex/src/features/notifications/models/trigger_event_model.dart';
import 'package:vocadex/src/features/notifications/utils/notification_scheduler.dart';

part 'local_notification_service.g.dart';

@riverpod
LocalNotificationService localNotificationService(Ref ref) {
  return LocalNotificationService(ref);
}

class LocalNotificationService {
  final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();
  final Ref _ref;
  bool _initialized = false;
  late final NotificationScheduler _scheduler;

  LocalNotificationService(this._ref);

  /// Initialize local notification service
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Initialize timezone
      tz.initializeTimeZones();

      // Android initialization settings
      const androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _notifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Initialize scheduler
      _scheduler = NotificationScheduler(this, _ref);

      _initialized = true;
      debugPrint('Local notification service initialized');
    } catch (e) {
      debugPrint('Error initializing local notification service: $e');
      rethrow;
    }
  }

  /// Request notification permission
  Future<bool> requestPermission() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidPlugin =
            _notifications.resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        if (androidPlugin != null) {
          final granted = await androidPlugin.requestNotificationsPermission();
          return granted ?? false;
        }
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        final iosPlugin = _notifications.resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>();

        if (iosPlugin != null) {
          final granted = await iosPlugin.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
          return granted ?? false;
        }
      }
      return true;
    } catch (e) {
      debugPrint('Error requesting local notification permission: $e');
      return false;
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidPlugin =
            _notifications.resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        if (androidPlugin != null) {
          final enabled = await androidPlugin.areNotificationsEnabled();
          return enabled ?? false;
        }
      }
      return true; // Assume enabled for iOS
    } catch (e) {
      debugPrint('Error checking local notification settings: $e');
      return false;
    }
  }

  /// Schedule a notification
  Future<void> scheduleNotification(
    NotificationModel notification,
    DateTime scheduledTime,
  ) async {
    try {
      await _notifications.zonedSchedule(
        notification.id.hashCode,
        notification.title,
        notification.body,
        tz.TZDateTime.from(scheduledTime, tz.local),
        _getNotificationDetails(),
        payload: jsonEncode(notification.data ?? {}),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );

      debugPrint(
          'Scheduled notification: ${notification.id} at $scheduledTime');
    } catch (e) {
      debugPrint('Error scheduling notification: $e');
      rethrow;
    }
  }

  /// Show immediate notification
  Future<void> showNotification(NotificationModel notification) async {
    try {
      await _notifications.show(
        notification.id.hashCode,
        notification.title,
        notification.body,
        _getNotificationDetails(),
        payload: jsonEncode(notification.data ?? {}),
      );

      debugPrint('Showed immediate notification: ${notification.id}');
    } catch (e) {
      debugPrint('Error showing notification: $e');
      rethrow;
    }
  }

  /// Cancel a notification
  Future<void> cancelNotification(String notificationId) async {
    try {
      await _notifications.cancel(notificationId.hashCode);
      debugPrint('Cancelled notification: $notificationId');
    } catch (e) {
      debugPrint('Error cancelling notification: $e');
      rethrow;
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _notifications.cancelAll();
      debugPrint('Cancelled all notifications');
    } catch (e) {
      debugPrint('Error cancelling all notifications: $e');
      rethrow;
    }
  }

  /// Set up trigger events
  Future<void> setupTriggerEvents(List<TriggerEvent> triggers) async {
    try {
      await _scheduler.scheduleAllTriggers(triggers);
      debugPrint(
          'Set up ${triggers.where((t) => t.enabled).length} trigger events');
    } catch (e) {
      debugPrint('Error setting up trigger events: $e');
      rethrow;
    }
  }

  /// Refresh word recommendation notifications with fresh content
  /// This should be called daily to ensure users get personalized word recommendations
  Future<void> refreshWordRecommendationNotifications(
      List<TriggerEvent> triggers) async {
    try {
      // Find word recommendation triggers
      final wordRecommendationTriggers = triggers
          .where((trigger) =>
              trigger.type == TriggerType.wordRecommendation && trigger.enabled)
          .toList();

      if (wordRecommendationTriggers.isEmpty) {
        debugPrint('No enabled word recommendation triggers found');
        return;
      }

      // Cancel existing word recommendation notifications
      for (final trigger in wordRecommendationTriggers) {
        await _scheduler.cancelTrigger(trigger.id);
      }

      // Reschedule with fresh content
      for (final trigger in wordRecommendationTriggers) {
        await _scheduler.scheduleTrigger(trigger);
      }

      debugPrint(
          'Refreshed ${wordRecommendationTriggers.length} word recommendation notifications');
    } catch (e) {
      debugPrint('Error refreshing word recommendation notifications: $e');
    }
  }

  /// Get notification details
  NotificationDetails _getNotificationDetails() {
    const androidDetails = AndroidNotificationDetails(
      'vocadex_notifications',
      'Vocadex Notifications',
      channelDescription: 'Notifications for Vocadex app',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: false,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    return const NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.id}');
    debugPrint('Payload: ${response.payload}');

    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        // TODO: Handle navigation based on notification data
        debugPrint('Notification data: $data');
      } catch (e) {
        debugPrint('Error parsing notification payload: $e');
      }
    }
  }
}
