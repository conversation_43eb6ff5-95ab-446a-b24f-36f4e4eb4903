import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/features/notifications/models/trigger_event_model.dart';
import 'package:vocadex/src/features/notifications/models/notification_model.dart';
import 'package:vocadex/src/features/notifications/services/word_recommendation_service.dart';

part 'dynamic_notification_processor.g.dart';

@riverpod
DynamicNotificationProcessor dynamicNotificationProcessor(Ref ref) {
  return DynamicNotificationProcessor(ref);
}

/// Service for processing dynamic notification content with template variables
class DynamicNotificationProcessor {
  final Ref _ref;

  DynamicNotificationProcessor(this._ref);

  /// Process a trigger event and return a notification with dynamic content
  Future<NotificationModel?> processNotification(TriggerEvent trigger) async {
    try {
      // Check if this trigger uses dynamic content
      final isDynamic = trigger.conditions?['isDynamic'] == true;
      final isTemplate = trigger.conditions?['template'] == true;

      if (!isDynamic || !isTemplate) {
        // Return static notification
        return _createStaticNotification(trigger);
      }

      // Handle different trigger types with dynamic content
      switch (trigger.type) {
        case TriggerType.wordRecommendation:
          return await _processWordRecommendationNotification(trigger);
        default:
          // Fallback to static notification for unsupported types
          return _createStaticNotification(trigger);
      }
    } catch (e) {
      debugPrint('Error processing dynamic notification: $e');
      // Fallback to static notification on error
      return _createStaticNotification(trigger);
    }
  }

  /// Process word recommendation notification with dynamic word insertion
  Future<NotificationModel?> _processWordRecommendationNotification(
      TriggerEvent trigger) async {
    try {
      // Get word recommendation service
      final wordService = _ref.read(wordRecommendationServiceProvider);

      // Check if user has words needing practice
      final hasWordsNeedingPractice = await wordService.hasWordsNeedingPractice();
      if (!hasWordsNeedingPractice) {
        debugPrint('No words needing practice - skipping notification');
        return null;
      }

      // Get a word recommendation
      final recommendedWord = await wordService.getWordRecommendation();
      if (recommendedWord == null) {
        debugPrint('No word recommendation available - skipping notification');
        return null;
      }

      // Process template variables in title and message
      final processedTitle = _processTemplate(trigger.title, {
        'word': _capitalizeWord(recommendedWord.word),
      });

      final processedMessage = _processTemplate(trigger.message, {
        'word': _capitalizeWord(recommendedWord.word),
      });

      // Create notification with processed content
      return NotificationModel(
        id: '${trigger.id}_${DateTime.now().millisecondsSinceEpoch}',
        title: processedTitle,
        body: processedMessage,
        type: NotificationType.local,
        status: NotificationStatus.pending,
        data: {
          'triggerId': trigger.id,
          'triggerType': trigger.type.name,
          'recommendedWordId': recommendedWord.id,
          'recommendedWord': recommendedWord.word,
          'masteryLevel': recommendedWord.masteryLevel,
        },
        createdAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error processing word recommendation notification: $e');
      return null;
    }
  }

  /// Create a static notification without dynamic content
  NotificationModel _createStaticNotification(TriggerEvent trigger) {
    return NotificationModel(
      id: '${trigger.id}_${DateTime.now().millisecondsSinceEpoch}',
      title: trigger.title,
      body: trigger.message,
      type: NotificationType.local,
      status: NotificationStatus.pending,
      data: {
        'triggerId': trigger.id,
        'triggerType': trigger.type.name,
      },
      createdAt: DateTime.now(),
    );
  }

  /// Process template string by replacing variables with values
  String _processTemplate(String template, Map<String, String> variables) {
    String processed = template;

    for (final entry in variables.entries) {
      final placeholder = '{${entry.key}}';
      processed = processed.replaceAll(placeholder, entry.value);
    }

    return processed;
  }

  /// Capitalize the first letter of a word
  String _capitalizeWord(String word) {
    if (word.isEmpty) return word;
    return word[0].toUpperCase() + word.substring(1).toLowerCase();
  }

  /// Get available template variables for a trigger type
  List<String> getAvailableVariables(TriggerType type) {
    switch (type) {
      case TriggerType.wordRecommendation:
        return ['word'];
      default:
        return [];
    }
  }

  /// Validate template string for a trigger type
  bool validateTemplate(String template, TriggerType type) {
    final availableVariables = getAvailableVariables(type);
    
    // Extract variables from template
    final variablePattern = RegExp(r'\{(\w+)\}');
    final matches = variablePattern.allMatches(template);
    
    for (final match in matches) {
      final variable = match.group(1);
      if (variable != null && !availableVariables.contains(variable)) {
        return false;
      }
    }
    
    return true;
  }

  /// Get template help text for a trigger type
  String getTemplateHelp(TriggerType type) {
    switch (type) {
      case TriggerType.wordRecommendation:
        return 'Available variables: {word} - The recommended word to practice';
      default:
        return 'No template variables available for this trigger type';
    }
  }

  /// Preview processed template with sample data
  String previewTemplate(String template, TriggerType type) {
    final sampleData = _getSampleData(type);
    return _processTemplate(template, sampleData);
  }

  /// Get sample data for template preview
  Map<String, String> _getSampleData(TriggerType type) {
    switch (type) {
      case TriggerType.wordRecommendation:
        return {'word': 'Magnificent'};
      default:
        return {};
    }
  }
}
